Change Log

Emoji | Signification
✨ | Nouvelle fonctionnalité (feature)
🐛 | Correction de bug (bug fix)
♻️ | Refactoring
🔥 | Suppression de code / fonctionnalité
🧪 | Ajout ou mise à jour de tests
📝 | Documentation
🧹 | Nettoyage du code
✅ | Ajout d’un test qui passe
🚀 | Amélioration des performances
Toutes les modifications notables apportées à ce projet seront documentées ici
## [1.1.8] (05/08/2025)
✨ | Nouvelle fonctionnalité (feature)
- Ajout de target_port dans la définition du service : il est désormais possible de définir un targetPort différent de servicePort. Par défaut, targetPort prend la valeur de servicePort.
## [1.1.7] (25/07/2025)
🐛 | Correction de bug (bug fix)
- Modification de la section envFromConfigMap.name pour définir un nom distincte pour la variable envFromConfigMap.env_name.
- Ajout de l'exposition par défaut du port HTTPS 443 pour les services

## [1.1.6] (24/07/2025)
🐛 | Correction de bug (bug fix)
- Modification de la section volumes pour prendre en compte les volumes non configMap.
- Correction d'une erreur sur le rendu de {{- toYaml $.Values.volumes | nindent 8 }}.

✨ | Nouvelle fonctionnalité (feature)
- Ajout de l’annotation checksum/config dans le Deployment pour forcer un redémarrage du pod lors de modifications du ConfigMap.
- Ajout du support de la variable d’environnement complexe via valueFrom.configMapKeyRef (envFromConfigMap).
- Génération du bloc env: même si seulement envFromConfigMap est défini (et non plus uniquement environmentVariables).

♻️ | Refactoring
- Refonte de la logique de génération de la section env: dans le Deployment.
- La valeur configFiles.name est désormais obligatoire : elle est utilisée comme nom explicite du ConfigMap.
- Géneration automatique des Routes pour tout type de service et d'environnement 
## [1.1.5] (5/06/2025)
✨ | Nouvelle fonctionnalité (feature)
- Exposer les backend pour les environnements ny et by 
## [1.1.4] (5/06/2025)
✨ | Nouvelle fonctionnalité (feature)
- Ajout de la posibilité de override le nom du configmap 

## [1.1.3] (4/06/2025)
✨ Nouvelle fonctionnalité (feature)
- Création d'un configmap sans le monter dans un volume
🐛 | Correction de bug (bug fix)
- Ajout de la posibilité de override le nom du service dans route

## [1.1.2] (2/06/2025)
✨ Nouvelle fonctionnalité (feature)
- Ajout de la posibilité de override le nom du service 

## [1.1.1] (16/04/2025)
♻️ Refactoring
- Changement du nom de l'imagePullSecrets,le nouveau nom est "nexus-registry-pull-secret"

## [1.1.0] (07/04/2025)
🚀 Amélioration
- Suppression du securityContext dans le déploiement

## [1.0.1] (27/03/2025)
🚀 Amélioration
- Ajout d’une condition pour ne pas créer de ConfigMap si elle n’est pas déclarée dans le fichier values.yaml
- Ajout d’une condition pour ne pas créer de volume s’il n'est  pas déclaré dans le fichier values.yaml

## [1.0.0] (27/03/2025)

### 🎉 Initial Release