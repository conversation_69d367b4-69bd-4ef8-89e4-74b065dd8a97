# Maven Release Management pour Helm Chart

Ce projet utilise Maven pour gérer automatiquement les releases de la chart Helm. Il sera exécuté par un job jenkins de release : [EAI_PIC_GITOPS_HELM__helm-shared-application-RELEASE](https://pic.master.eurafric.com/jenkins/job/EAI_PIC_GITOPS_HELM__helm-shared-application/job/EAI_PIC_GITOPS_HELM__helm-shared-application-RELEASE/)

## Configuration

### Prérequis
- Maven 3.6+
- Java 8+

### Plugins Maven utilisés
- **maven-resources-plugin** : v3.2.0
  - Gestion des ressources et filtrage des fichiers
- **maven-assembly-plugin** : v3.3.0
  - Création du package ZIP final
- **maven-release-plugin** : v2.5.3
  - Gestion automatisée des releases
- **maven-deploy-plugin** : v2.8.2
  - Déploiement vers Nexus

## Utilisation

### Build local pour vérification
```bash
mvn clean package
```
Cela génère un ZIP contenant la chart Helm avec la version mise à jour dans `target/`.


## Fonctionnement

1. **Gestion des versions** : La version dans `Chart.yaml` est automatiquement mise à jour depuis `project.version` du POM
2. **Assembly** : Un ZIP est créé contenant tous les fichiers de la chart Helm
3. **Release** : Le maven-release-plugin gère automatiquement :
   - Mise à jour des versions (suppression de -SNAPSHOT)
   - Création du tag Git
   - Build et déploiement vers Nexus
   - Mise à jour vers la prochaine version SNAPSHOT

## Structure générée

```
target/
├── helm-chart/                    # Fichiers de la chart avec versions mises à jour
│   ├── Chart.yaml                # Version extraite du POM
│   ├── values.yaml
│   ├── templates/
│   └── ...
└── helm-shared-application-X.Y.Z.zip  # Archive finale
```
