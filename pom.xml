<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>ma.eai.helm</groupId>
    <artifactId>helm-shared-application</artifactId>
    <version>1.1.19-SNAPSHOT</version>
    <packaging>pom</packaging>
    
    <name>Helm Shared Application Chart</name>
    <description>Helm chart for shared application deployment</description>
    
    <scm>
        <connection>scm:git:************************:GITOPS/HELM/helm-shared-application.git</connection>
        <developerConnection>scm:git:************************:GITOPS/HELM/helm-shared-application.git</developerConnection>
        <url>https://pic.ref.eurafric.com/gitlab/GITOPS/HELM/helm-shared-application.git</url>
        <tag>TAG_RELEASE_helm-shared-application-1.1.19</tag>
    </scm>

    
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.build.timestamp.format>dd/MM/yyyy HH:mm</maven.build.timestamp.format>
        <timestamp>${maven.build.timestamp}</timestamp>
        
        <!-- Plugin versions -->
        <maven-resources-plugin.version>3.2.0</maven-resources-plugin.version>
        <maven-assembly-plugin.version>3.3.0</maven-assembly-plugin.version>
        <maven-release-plugin.version>2.5.3</maven-release-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <exec-maven-plugin.version>3.1.0</exec-maven-plugin.version>
        
        <!-- Repository configuration -->
        <repository-name>EAI-Helm-Repository</repository-name>
    </properties>
    
    <repositories>
        <repository>
            <id>maven-public</id>
            <name>EAI Maven REPO</name>
            <url>https://pic.ref.eurafric.com/nexus/repository/maven-public</url>
        </repository>
    </repositories>
    
    <pluginRepositories>
        <pluginRepository>
            <id>maven-public</id>
            <name>EAI Maven REPO</name>
            <url>https://pic.ref.eurafric.com/nexus/repository/maven-public</url>
        </pluginRepository>
    </pluginRepositories>
    
    <build>
        <resources>
            <resource>
                <directory>.</directory>
                <filtering>true</filtering>
                <includes>
                    <include>Chart.yaml</include>
                    <include>values.yaml</include>
                    <include>templates/**</include>
                    <include>README.md</include>
                    <include>CHANGELOG.md</include>
                </includes>
                <targetPath>${project.build.directory}/helm-chart</targetPath>
            </resource>
        </resources>
        
        <plugins>
            <!-- Maven Resources Plugin - Pour filtrer les fichiers et remplacer les versions -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin.version}</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
                <executions>
                    <execution>
                        <id>copy-helm-files</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>resources</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            
            <!-- Maven Assembly Plugin - Pour créer le ZIP de la chart -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>${maven-assembly-plugin.version}</version>
                <configuration>
                    <descriptors>
                        <descriptor>src/assembly/helm-chart.xml</descriptor>
                    </descriptors>
                    <finalName>${project.artifactId}-${project.version}</finalName>
                    <appendAssemblyId>false</appendAssemblyId>
                </configuration>
                <executions>
                    <execution>
                        <id>create-helm-chart-zip</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            
            <!-- Maven Release Plugin - Pour gérer les releases automatiquement -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>${maven-release-plugin.version}</version>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                    <useReleaseProfile>false</useReleaseProfile>
                    <goals>deploy</goals>
                    <scmCommentPrefix>[maven-release] </scmCommentPrefix>
                    <tagNameFormat>TAG_RELEASE_@{project.artifactId}-@{project.version}</tagNameFormat>
                    <preparationGoals>clean verify</preparationGoals>
                    <completionGoals>clean</completionGoals>
                </configuration>
            </plugin>
            
            <!-- Maven Deploy Plugin - Désactivé car on utilise le script bash -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven-deploy-plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <!-- Exec Plugin - Pour exécuter le script de publication Helm -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>${exec-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>publish-helm-chart</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>bash</executable>
                            <arguments>
                                <argument>publish-helm-chart.sh</argument>
                                <argument>${project.build.directory}/${project.artifactId}-${project.version}.tgz</argument>
                            </arguments>
                            <workingDirectory>${project.basedir}</workingDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    
</project>
