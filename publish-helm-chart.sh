#!/bin/bash

# Script de publication de chart Helm vers Nexus
# Usage: ./publish-helm-chart.sh <path-to-helm-package>

set -e  # Arrêter le script en cas d'erreur

# Configuration
NEXUS_URL="https://pic.ref.eurafric.com/nexus/repository/EAI-Helm-Repository"
NEXUS_USER="gitops-helm-user"
NEXUS_PASSWORD="Pic@2025*"

# Vérification des paramètres
if [ $# -ne 1 ]; then
    echo "❌ Usage: $0 <path-to-helm-package>"
    echo "   Exemple: $0 target/helm-shared-application-1.1.19.tgz"
    exit 1
fi

HELM_PACKAGE_PATH="$1"
HELM_PACKAGE_NAME=$(basename "$HELM_PACKAGE_PATH")

# Vérification que le fichier existe
if [ ! -f "$HELM_PACKAGE_PATH" ]; then
    echo "❌ Erreur: Le fichier $HELM_PACKAGE_PATH n'existe pas"
    exit 1
fi

echo "🚀 Publication de la chart Helm vers Nexus..."
echo "   📦 Package: $HELM_PACKAGE_NAME"
echo "   🎯 Repository: $NEXUS_URL"

# Upload vers Nexus
echo "📤 Upload en cours..."
curl -k -u "$NEXUS_USER:$NEXUS_PASSWORD" -T "$HELM_PACKAGE_PATH" "$NEXUS_URL/$HELM_PACKAGE_NAME"

# Vérification du statut de l'upload
if [ $? -eq 0 ]; then
    echo "✅ Helm Chart pushé avec succès sur Nexus: $NEXUS_URL/$HELM_PACKAGE_NAME"
    echo "🎉 Publication terminée avec succès !"
else
    echo "❌ Erreur lors du push sur Nexus."
    exit 1
fi
