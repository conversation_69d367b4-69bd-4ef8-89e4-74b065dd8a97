<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
    
    <id>helm-chart</id>
    <formats>
        <format>zip</format>
    </formats>
    
    <includeBaseDirectory>false</includeBaseDirectory>
    
    <fileSets>
        <!-- Include processed helm chart files from target directory -->
        <fileSet>
            <directory>${project.build.directory}/helm-chart</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>**/*</include>
            </includes>
        </fileSet>
    </fileSets>
    
</assembly>
