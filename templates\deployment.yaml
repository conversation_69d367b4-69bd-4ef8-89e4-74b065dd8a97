{{- range $serviceName, $serviceConfig := .Values.services }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment-{{ $serviceName }}-{{ $.Values.application_name }}
  namespace: {{ $.Values.namespace.name }}
  labels:
    app: {{ $.Values.application_name }}
spec:
  selector:
    matchLabels:
      app: {{ $.Values.application_name }}
  template:
    metadata:
      labels:
        app: {{ $.Values.application_name }}
      {{- if $.Values.configFiles  }} 
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") $ | sha256sum }}
      {{ end }}
    spec:
      imagePullSecrets:
        - name: nexus-registry-pull-secret
      serviceAccountName: sa-{{ $.Values.trigramme }}
      containers:
        - name: {{ $.Values.application_name }}
          image: "{{ $serviceConfig.image.repository }}:{{ $serviceConfig.image.tag  }}"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: {{ $serviceConfig.service_port }}
              protocol: TCP
          {{- if or $serviceConfig.environmentVariables $serviceConfig.envFromConfigMap }}
          env:
            {{- if $serviceConfig.environmentVariables }} 
            {{- range $k, $v := $serviceConfig.environmentVariables }}
            - name: {{ $k }}
              value: "{{ $v }}"
            {{- end }}
            {{- end }}
            {{- if $serviceConfig.envFromConfigMap }}
            - name: {{ $serviceConfig.envFromConfigMap.env_name }}
              valueFrom:
                configMapKeyRef:
                  name: {{ $serviceConfig.envFromConfigMap.name }}
                  key: {{ $serviceConfig.envFromConfigMap.key }}
            {{- end }}
          {{- end }}
          {{- if $serviceConfig.secret }}
          envFrom:
            - secretRef:
                name: secret-{{ $serviceName }}-{{ $.Values.application_name }}
          {{- end }}
          {{- if $serviceConfig.probes.enabled }}
            {{- with $serviceConfig.probes.livenessProbe }}
          livenessProbe:
            {{- toYaml . | nindent 12 }}
            {{- end }}
            {{- with $serviceConfig.probes.readinessProbe }}
          readinessProbe:
            {{- toYaml . | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- with $serviceConfig.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
         {{- if  $serviceConfig.volumeMounts  }}
          volumeMounts:
              {{- toYaml $serviceConfig.volumeMounts | nindent 12 }}
          {{- end }}
      {{- if  $.Values.volumes }}
      volumes:
          {{- toYaml $.Values.volumes | nindent 8 }}
        {{- end }}
{{- end }}
