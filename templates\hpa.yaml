{{- range $serviceName, $serviceConfig := .Values.services }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $serviceName }}-{{ $.Values.application_name }}
  namespace: {{ $.Values.namespace.name }}
  labels:
    app: {{ $.Values.application_name }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: deployment-{{ $serviceName }}-{{ $.Values.application_name }}
  minReplicas: {{ $serviceConfig.autoscaling.minReplicas }}
  maxReplicas: {{ $serviceConfig.autoscaling.maxReplicas }}
  metrics:
    {{- if $serviceConfig.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ $serviceConfig.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if $serviceConfig.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ $serviceConfig.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
---
{{- end }}
