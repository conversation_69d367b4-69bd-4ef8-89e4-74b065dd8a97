{{- range $serviceName, $serviceConfig := .Values.services }}
apiVersion: route.openshift.io/v1
kind: Route
metadata:
  name: route-{{ $.Values.application_name }}
  namespace: {{ $.Values.namespace.name }}
  labels:
    app: {{ $.Values.application_name }}
  annotations:
    cert-utils-operator.redhat-cop.io/certs-from-secret: eai-tls
spec:
  to:
    kind: Service
    name: {{ if $serviceConfig.service_name_override }}{{ $serviceConfig.service_name_override }}{{ else }}{{ printf "service-%s-%s" $serviceName $.Values.application_name }}{{ end }}
    weight: 100
  {{- if (eq $.Values.env "ny") }}
  host: {{ $.Values.application_name }}-{{ $.Values.namespace.name }}.apps.bbony-ocpmut-app.cloudeai.com
  {{- else if eq $.Values.env "dy" }}
  host: {{ $.Values.application_name }}-{{ $.Values.namespace.name }}.apps.bbody-ocpmut-app.cloudeai.com
  {{- else if eq $.Values.env "by" }}
  host: {{ $.Values.application_name }}-{{ $.Values.namespace.name }}.apps.bboby-ocpmut-app.cloudeai.com
  {{- end }}
  port:
    targetPort: {{ $serviceConfig.service_port }}
  tls:
    termination: edge
    insecureEdgeTerminationPolicy: Redirect
{{- end }}
