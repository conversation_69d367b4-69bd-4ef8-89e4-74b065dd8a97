{{- range $serviceName, $serviceConfig := .Values.services }}
apiVersion: v1
kind: Service
metadata:
  name: {{ if $serviceConfig.service_name_override }}{{ $serviceConfig.service_name_override }}{{ else }}{{ printf "service-%s-%s" $serviceName $.Values.application_name }}{{ end }}
  namespace: {{ $.Values.namespace.name }}
  labels:
    app: {{ $.Values.application_name }}
spec:
  ports:
    - name: http
      port: {{ $serviceConfig.service_port  }}
      targetPort: {{ $serviceConfig.target_port | default $serviceConfig.service_port }}
      protocol: TCP
    - name: https
      port: {{ $serviceConfig.https_port | default 8443 }}
      targetPort: {{ $serviceConfig.https_port | default 8443 }}
      protocol: TCP
  selector:
    app: {{ $.Values.application_name }}
---
{{- end }}
