# helm-shared-application:
#   application_name: "coresys-oc-api-document"
#   trigramme: "coresys"
#   # env type dy or ny 
#   env: dy
#   namespace: 
#     enabled: false
#     name: "eai-omnicanal-by-support-ns-01" 
#     size: "large"
#     resourcequota:
#       enabled: false
#   services: 
#     backend:
#       application_type: back
#       image:
#         repository: "pic-brm.eurafric.com:8085/eai-coresys/ma.eai.coresys.oc.api/coresys-oc-api-document@sha256"
#         tag: "6620c68d03185d0c469a05c88f0cb62054d78e78fc2077c5b4c032ed9f250f58"
#       service_port: 8080
#       service_name_override: "custom-backend"
#       environmentVariables:
#         minio-access-secret: 2ECHKJ02RPRU5MQODAJLSM5P2NNHD3LO
#         datasource-url: *******************************************************************************************************************************
#         aes256-secret-key: mysecretkeyfortos
#         multipart-max-size: '1200000'
#         kaspersky-scan-url: 'http://*************:3310/api/v3.0/scanmemory'
#         aes256-salt-key: thesaltkeyttoencodewith
#       secret:
#         name: support-postgresql
#         data:
#           PG_DEFAULT_PASSWORD: password132
#           POSTGRES_PASSWORD: password132
#           datasource-password: pa654321
#           datasource-username: postgres
#       resources: 
#         limits:
#           cpu: 500m
#           memory: 1Gi
#         requests:
#           cpu: 125m
#           memory: 125m
#       autoscaling:
#         minReplicas: 5
#         maxReplicas: 5
#         targetCPUUtilizationPercentage: 80
#         targetMemoryUtilizationPercentage: 80
#       probes:
#         enabled: false
#         livenessProbe:
#           httpGet:
#             path: /
#             port: http
#         readinessProbe:
#           httpGet:
#             path: /
#             port: http
#       volumeMounts:
#         - mountPath: /usr/local/apache2/htdocs/admin-front/env/env-config.js
#           name: env-config
#           subPath: env-config.js
#         - mountPath: /usr/local/apache2/conf/extra/admin.conf
#           name: env-config
#           subPath: admin.conf
#         - mountPath: /usr/local/apache2/conf/httpd.conf
#           name: env-config
#           subPath: httpd.conf
#         - mountPath: /usr/local/apache2/htdocs/admin-front/.htaccess
#           name: env-config
#           subPath: .htaccess
#   volumes:
#     - configMap:
#         name: admin-front-config
#       name: env-config
#   configFiles:
#     env-config.js: |
#         window._env_ = {
#             REACT_APP_RUNTIME_USE_OAUTH2: false,
#             REACT_APP_RUNTIME_GATEWAY_URL: "http://__GATEWAY-SERVICE_IP_ADDRESS__:30003/",
#             REACT_APP_RUNTIME_KEYCLOAK_URL: "http://__GATEWAY-SERVICE_IP_ADDRESS__:30003/",
#             REACT_APP_RUNTIME_KEYCLOAK_REALM: "standard-adria",
#             REACT_APP_RUNTIME_KEYCLOAK_CLIENT_ID: "admin-front"
#         }
#     admin.conf: |
#       <VirtualHost *:80>
#           #SSLEngine on
#           #SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1

#           #ServerName nomdedomainadmin:443
#           #ServerAlias nomdedomainadmin

#           DocumentRoot /usr/local/apache2/htdocs/admin-front

#           Header always append X-Frame-Options SAMEORIGIN
#           #Header always set Content-Security-Policy: "script-src 'self';"

#           ErrorLog /usr/local/apache2/logs/error.log
#           CustomLog /usr/local/apache2/logs/access.log combined

#           #SSLCertificateFile    /opt/ssl/votre_domain_name.crt
#           #SSLCertificateKeyFile /opt/ssl/votre_cle_prive.key
#           #SSLCACertificateFile  /opt/ssl/root_certificate_autority.crt

#           #<Files ~ "\.(cgi|shtml|phtml|php3?)$">
#           #       SSLOptions +StdEnvVars
#           #</Files>

#           #<Directory "/var/www/cgi-bin/">
#           #       SSLOptions +StdEnvVars
#           #</Directory>
#       </VirtualHost>
#       #Listen 443

